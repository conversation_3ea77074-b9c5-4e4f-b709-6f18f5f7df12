import 'dart:io';
import 'package:image/image.dart' as img;

// This script creates a transparent PNG image for the splash screen
// Run with: dart lib/tools/create_empty_splash.dart

void main() async {
  // Create a 1x1 transparent image
  final image = img.Image(width: 1, height: 1);

  // Make it transparent
  image.clear(img.ColorRgba8(0, 0, 0, 0));

  // Encode to PNG
  final pngData = img.encodePng(image);

  // Save to Android resource directories
  final androidResDir = Directory(
    '/Users/<USER>/Documents/augment-projects/Nepali Results/nepali_results/android/app/src/main/res',
  );

  // Create directories for different densities
  final directories = [
    'drawable-hdpi',
    'drawable-mdpi',
    'drawable-xhdpi',
    'drawable-xxhdpi',
    'drawable-xxxhdpi',
    'drawable-night-hdpi',
    'drawable-night-mdpi',
    'drawable-night-xhdpi',
    'drawable-night-xxhdpi',
    'drawable-night-xxxhdpi',
  ];

  for (final dir in directories) {
    final directory = Directory('${androidResDir.path}/$dir');
    if (await directory.exists()) {
      // Save splash.png
      final splashFile = File('${directory.path}/splash.png');
      await splashFile.writeAsBytes(pngData);

      // Save android12splash.png
      final android12SplashFile = File('${directory.path}/android12splash.png');
      await android12SplashFile.writeAsBytes(pngData);

      print('Created transparent PNG at: ${splashFile.path}');
      print('Created transparent PNG at: ${android12SplashFile.path}');
    }
  }
}
