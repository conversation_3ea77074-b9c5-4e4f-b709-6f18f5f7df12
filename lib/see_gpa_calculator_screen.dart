import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class SeeGpaCalculatorScreen extends StatefulWidget {
  const SeeGpaCalculatorScreen({super.key});

  @override
  State<SeeGpaCalculatorScreen> createState() => _SeeGpaCalculatorScreenState();
}

class _SeeGpaCalculatorScreenState extends State<SeeGpaCalculatorScreen> {
  // Standard SEE subjects
  final List<String> _defaultSubjects = [
    'English',
    'Nepali',
    'Mathematics',
    'Science',
    'Social Studies',
    'Health, Population & Environment',
    'Optional I',
    'Optional II',
  ];

  // Map to store marks for each subject
  final Map<String, TextEditingController> _marksControllers = {};
  
  // Map to store calculated grades and grade points
  final Map<String, String> _grades = {};
  final Map<String, double> _gradePoints = {};
  
  // Results
  double _calculatedGpa = 0.0;
  String _overallGrade = '';
  String _description = '';
  bool _hasCalculated = false;
  
  // Form key for validation
  final _formKey = GlobalKey<FormState>();
  
  // SEE Grading System
  final Map<String, Map<String, dynamic>> _seeGradingSystem = {
    'A+': {'range': [90, 100], 'point': 4.0, 'description': 'Outstanding'},
    'A': {'range': [80, 89], 'point': 3.6, 'description': 'Excellent'},
    'B+': {'range': [70, 79], 'point': 3.2, 'description': 'Very Good'},
    'B': {'range': [60, 69], 'point': 2.8, 'description': 'Good'},
    'C+': {'range': [50, 59], 'point': 2.4, 'description': 'Satisfactory'},
    'C': {'range': [40, 49], 'point': 2.0, 'description': 'Acceptable'},
    'D+': {'range': [30, 39], 'point': 1.6, 'description': 'Partially Acceptable'},
    'D': {'range': [20, 29], 'point': 1.2, 'description': 'Insufficient'},
    'E': {'range': [0, 19], 'point': 0.8, 'description': 'Very Insufficient'},
  };
  
  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }
  
  @override
  void dispose() {
    // Dispose all text controllers
    for (var controller in _marksControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }
  
  void _initializeControllers() {
    for (var subject in _defaultSubjects) {
      _marksControllers[subject] = TextEditingController();
      _grades[subject] = '-';
      _gradePoints[subject] = 0.0;
    }
  }
  
  void _resetForm() {
    setState(() {
      for (var subject in _defaultSubjects) {
        _marksControllers[subject]?.clear();
        _grades[subject] = '-';
        _gradePoints[subject] = 0.0;
      }
      _hasCalculated = false;
      _calculatedGpa = 0.0;
      _overallGrade = '';
      _description = '';
    });
  }
  
  String _getGradeFromMarks(double marks) {
    for (var entry in _seeGradingSystem.entries) {
      final range = entry.value['range'] as List<int>;
      if (marks >= range[0] && marks <= range[1]) {
        return entry.key;
      }
    }
    return 'N/A';
  }
  
  double _getGradePointFromGrade(String grade) {
    return _seeGradingSystem[grade]?['point'] as double? ?? 0.0;
  }
  
  String _getDescriptionFromGrade(String grade) {
    return _seeGradingSystem[grade]?['description'] as String? ?? '';
  }
  
  void _calculateGpa() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    double totalPoints = 0;
    int validSubjectsCount = 0;
    
    for (var subject in _defaultSubjects) {
      final marksText = _marksControllers[subject]?.text ?? '';
      if (marksText.isNotEmpty) {
        final marks = double.parse(marksText);
        final grade = _getGradeFromMarks(marks);
        final gradePoint = _getGradePointFromGrade(grade);
        
        _grades[subject] = grade;
        _gradePoints[subject] = gradePoint;
        
        totalPoints += gradePoint;
        validSubjectsCount++;
      }
    }
    
    setState(() {
      if (validSubjectsCount > 0) {
        _calculatedGpa = totalPoints / validSubjectsCount;
        _hasCalculated = true;
        _overallGrade = _getOverallGradeFromGpa(_calculatedGpa);
        _description = _getDescriptionFromGrade(_overallGrade);
      } else {
        _calculatedGpa = 0.0;
        _hasCalculated = true;
        _overallGrade = 'N/A';
        _description = 'Please enter marks for at least one subject';
      }
    });
  }
  
  String _getOverallGradeFromGpa(double gpa) {
    if (gpa >= 3.6) return 'A+';
    if (gpa >= 3.2) return 'A';
    if (gpa >= 2.8) return 'B+';
    if (gpa >= 2.4) return 'B';
    if (gpa >= 2.0) return 'C+';
    if (gpa >= 1.6) return 'C';
    if (gpa >= 1.2) return 'D+';
    if (gpa >= 0.8) return 'D';
    return 'E';
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'SEE GPA Calculator',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Center(
                  child: Text(
                    'SEE GPA Calculator',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                
                // Subtitle
                Center(
                  child: Text(
                    'Enter your marks (0-100) for each subject',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                
                // Subject Marks Input
                ...List.generate(_defaultSubjects.length, (index) {
                  final subject = _defaultSubjects[index];
                  return _buildSubjectMarksRow(subject);
                }),
                
                const SizedBox(height: 24),
                
                // Calculate Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _calculateGpa,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Calculate GPA',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // Clear Button
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: _resetForm,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.primary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      'Clear Form',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Results
                if (_hasCalculated)
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Your SEE GPA Result',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'GPA:',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                _calculatedGpa.toStringAsFixed(2),
                                style: GoogleFonts.poppins(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Grade:',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                _overallGrade,
                                style: GoogleFonts.poppins(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: _getColorForGrade(_overallGrade),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Description:',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                _description,
                                style: GoogleFonts.poppins(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: _getColorForGrade(_overallGrade),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          const Divider(),
                          const SizedBox(height: 8),
                          Text(
                            'Subject Grades',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ...List.generate(_defaultSubjects.length, (index) {
                            final subject = _defaultSubjects[index];
                            final marksText = _marksControllers[subject]?.text ?? '';
                            if (marksText.isEmpty) return const SizedBox.shrink();
                            
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    flex: 3,
                                    child: Text(
                                      subject,
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Text(
                                      marksText,
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Text(
                                      _grades[subject] ?? '-',
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.bold,
                                        color: _getColorForGrade(_grades[subject] ?? ''),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 1,
                                    child: Text(
                                      (_gradePoints[subject] ?? 0.0).toStringAsFixed(1),
                                      textAlign: TextAlign.center,
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  Widget _buildSubjectMarksRow(String subject) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              subject,
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: TextFormField(
              controller: _marksControllers[subject],
              decoration: InputDecoration(
                hintText: '0-100',
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d{0,3}$')),
              ],
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final marks = double.tryParse(value);
                  if (marks == null) {
                    return 'Invalid';
                  }
                  if (marks < 0 || marks > 100) {
                    return 'Range: 0-100';
                  }
                }
                return null;
              },
              onChanged: (value) {
                if (_hasCalculated) {
                  setState(() {
                    _hasCalculated = false;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getColorForGrade(String grade) {
    switch (grade) {
      case 'A+':
        return Colors.purple;
      case 'A':
        return Colors.indigo;
      case 'B+':
        return Colors.blue;
      case 'B':
        return Colors.teal;
      case 'C+':
        return Colors.green;
      case 'C':
        return Colors.amber.shade700;
      case 'D+':
        return Colors.orange;
      case 'D':
        return Colors.deepOrange;
      case 'E':
        return Colors.red;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}
