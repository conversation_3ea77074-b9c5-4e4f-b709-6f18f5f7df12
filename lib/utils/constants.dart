import 'package:flutter/material.dart';

class AppConstants {
  static const String appName = 'Nepali Results';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Check your results easily';

  // App Store URLs
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.example.nepali_results_working';
  static const String appStoreUrl = 'https://apps.apple.com/app/nepali-results/id123456789';

  // Contact Information
  static const String supportEmail = '<EMAIL>';
  static const String developerName = 'Nepali Results Team';

  // Social Media
  static const String facebookUrl = 'https://facebook.com/nepaliresults';
  static const String twitterUrl = 'https://twitter.com/nepaliresults';

  // Privacy and Terms
  static const String privacyPolicyUrl = 'https://nepaliresults.com/privacy';
  static const String termsOfServiceUrl = 'https://nepaliresults.com/terms';

  // Colors
  static const Color primaryColor = Color(0xFF1976D2);
  static const Color secondaryColor = Color(0xFF42A5F5);

  // Result URLs
  static const Map<String, String> resultUrls = {
    'SEE': 'https://see.ntc.net.np/result',
    'HSEB': 'https://hseb.edu.np/result',
    'TU': 'https://exam.tu.edu.np/',
    'KU': 'https://ku.edu.np/result',
    'PU': 'https://pu.edu.np/result',
    'CTEVT': 'https://ctevt.org.np/result',
    'DV': 'https://dv.gov.np/result',
    'IPO': 'https://ipo.gov.np/result',
  };

  // Category Descriptions
  static const Map<String, String> categoryDescriptions = {
    'SEE': 'Secondary Education Examination Results',
    'HSEB': 'Higher Secondary Education Board Results',
    'TU': 'Tribhuvan University Results',
    'KU': 'Kathmandu University Results',
    'PU': 'Pokhara University Results',
    'CTEVT': 'Council for Technical Education and Vocational Training',
    'DV': 'Diversity Visa Results',
    'IPO': 'Initial Public Offering Results',
  };

  // Category Icons
  static const Map<String, IconData> categoryIcons = {
    'SEE': Icons.school,
    'HSEB': Icons.account_balance,
    'TU': Icons.location_city,
    'KU': Icons.business,
    'PU': Icons.landscape,
    'CTEVT': Icons.engineering,
    'DV': Icons.public,
    'IPO': Icons.trending_up,
  };

  // Category Colors
  static const Map<String, Color> categoryColors = {
    'SEE': Colors.blue,
    'HSEB': Colors.indigo,
    'TU': Colors.deepPurple,
    'KU': Colors.teal,
    'PU': Colors.lightGreen,
    'CTEVT': Colors.orange,
    'DV': Colors.green,
    'IPO': Colors.amber,
  };

  // SMS Information
  static const Map<String, Map<String, String>> smsInfo = {
    'SEE': {
      'number': '31001',
      'format': 'SEE <symbol_number>',
      'example': 'SEE **********',
    },
    'HSEB': {
      'number': '31002',
      'format': 'HSEB <symbol_number>',
      'example': 'HSEB **********',
    },
  };
}
