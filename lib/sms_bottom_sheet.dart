import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/result_category.dart';

class SmsBottomSheet extends StatelessWidget {
  final SmsInfo smsInfo;

  const SmsBottomSheet({
    super.key,
    required this.smsInfo,
  });

  Future<void> _sendSms() async {
    final Uri smsUri = Uri(
      scheme: 'sms',
      path: smsInfo.sendTo,
      queryParameters: {'body': smsInfo.example},
    );

    try {
      await launchUrl(smsUri);
    } catch (e) {
      debugPrint('Error launching SMS: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Check Result via SMS',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 16),
          _buildInfoRow(context, 'Format:', smsInfo.format),
          const SizedBox(height: 12),
          _buildInfoRow(context, 'Send to:', smsInfo.sendTo),
          const SizedBox(height: 12),
          _buildInfoRow(context, 'Example:', smsInfo.example),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.sms),
              label: const Text('Send SMS'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              onPressed: _sendSms,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Note: Standard SMS rates may apply.',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ],
    );
  }
}
