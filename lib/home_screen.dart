import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'result_category.dart';
import 'constants.dart';
import 'theme_provider.dart';
import 'utils/notification_provider.dart';
import 'grid_category_card.dart';
import 'date_display.dart';
import 'result_page.dart';
import 'about_page.dart';
import 'terms_page.dart';
import 'privacy_policy_page.dart';
import 'feedback_page.dart';
import 'notification_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late List<ResultCategory> _categories;

  @override
  void initState() {
    super.initState();
    _initializeCategories();
  }

  void _initializeCategories() {
    // Create categories from constants
    _categories =
        AppConstants.resultUrls.entries.map((entry) {
          final categoryName = entry.key;
          return ResultCategory(
            name: categoryName,
            description: AppConstants.categoryDescriptions[categoryName] ?? '',
            icon:
                AppConstants.categoryIcons[categoryName] ?? Icons.help_outline,
            resultUrls: entry.value,
            smsInfo: AppConstants.smsInfo[categoryName],
          );
        }).toList();

    // Sort categories to ensure specific order
    _categories.sort((a, b) {
      // Define the order of categories
      const order = ['SEE', 'HSEB', 'TU', 'KU', 'PU', 'CTEVT', 'DV', 'IPO'];
      return order.indexOf(a.name).compareTo(order.indexOf(b.name));
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:
            Theme.of(context).brightness == Brightness.dark
                ? Theme.of(context).primaryColor.withAlpha(80)
                : Colors.lightBlue.shade200,
        elevation: 0,
        toolbarHeight: 70, // Further increased height for better display
        title: DateDisplay(isCompact: false),
        centerTitle: true,
        actions: [
          // Notification Icon with Badge
          Consumer<NotificationProvider>(
            builder: (context, notificationProvider, _) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined),
                    tooltip: 'Notifications',
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationScreen(),
                        ),
                      );
                    },
                    // Add ripple effect with splash radius
                    splashRadius: 24,
                  ),
                  // Only show badge if notifications are enabled and count > 0
                  if (notificationProvider.areNotificationsEnabled &&
                      notificationProvider.notificationCount > 0)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.error,
                          shape: BoxShape.circle,
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Center(
                          child: Text(
                            notificationProvider.notificationCount > 9
                                ? '9+'
                                : notificationProvider.notificationCount
                                    .toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          // Theme Toggle
          IconButton(
            icon: Icon(
              Provider.of<ThemeProvider>(context).isDarkMode
                  ? Icons.light_mode
                  : Icons.dark_mode,
            ),
            tooltip: 'Toggle Theme',
            onPressed: () {
              Provider.of<ThemeProvider>(context, listen: false).toggleTheme();
            },
            // Add ripple effect with splash radius
            splashRadius: 24,
          ),
          // More Options Menu
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: Theme.of(context).appBarTheme.foregroundColor,
            ),
            tooltip: 'More Options',
            position: PopupMenuPosition.under,
            offset: const Offset(0, 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            onSelected: (value) {
              switch (value) {
                case 'about':
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const AboutPage()),
                  );
                  break;
                case 'terms':
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const TermsPage()),
                  );
                  break;
                case 'privacy':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PrivacyPolicyPage(),
                    ),
                  );
                  break;
                case 'feedback':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const FeedbackPage(),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (BuildContext context) {
              return [
                PopupMenuItem<String>(
                  value: 'about',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'About App',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'terms',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.description_outlined,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Terms & Conditions',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'privacy',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.privacy_tip_outlined,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Privacy Policy',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'feedback',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.feedback_outlined,
                        size: 20,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'Feedback',
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ];
            },
          ),
        ],
      ),

      body: SafeArea(
        child: Column(
          children: [
            // Curved header with light blue background
            Container(
              height: 120,
              decoration: BoxDecoration(
                gradient:
                    Theme.of(context).brightness == Brightness.dark
                        ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            const Color(
                              0xFF1A237E,
                            ).withAlpha(200), // Deep indigo
                            const Color(0xFF0D47A1).withAlpha(180), // Deep blue
                            const Color(
                              0xFF1A1A2E,
                            ).withAlpha(150), // Dark blue-black
                          ],
                          stops: const [0.0, 0.5, 1.0],
                        )
                        : LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.lightBlue.shade200,
                            Colors.lightBlue.shade50,
                          ],
                        ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? const Color(0xFF3F51B5).withAlpha(
                              80,
                            ) // Indigo shadow
                            : Colors.black.withAlpha(20),
                    blurRadius:
                        Theme.of(context).brightness == Brightness.dark
                            ? 12
                            : 8,
                    spreadRadius:
                        Theme.of(context).brightness == Brightness.dark ? 2 : 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppConstants.appName,
                      style: GoogleFonts.poppins(
                        fontSize:
                            Theme.of(context).brightness == Brightness.dark
                                ? 32
                                : 28,
                        fontWeight: FontWeight.bold,
                        letterSpacing:
                            Theme.of(context).brightness == Brightness.dark
                                ? 1.0
                                : 0.5,
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.blue.shade800,
                        shadows:
                            Theme.of(context).brightness == Brightness.dark
                                ? [
                                  Shadow(
                                    color: const Color(
                                      0xFF2196F3,
                                    ).withAlpha(150),
                                    blurRadius: 3,
                                    offset: const Offset(0, 1),
                                  ),
                                ]
                                : null,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Check your results easily",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight:
                            Theme.of(context).brightness == Brightness.dark
                                ? FontWeight.w600
                                : FontWeight.w500,
                        letterSpacing: 0.5,
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white.withAlpha(220)
                                : Colors.blue.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Grid of result categories
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: GridView.builder(
                  physics: const BouncingScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount:
                        MediaQuery.of(context).size.width > 600 ? 3 : 2,
                    childAspectRatio: 0.85,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: _categories.length,
                  itemBuilder: (context, index) {
                    final category = _categories[index];
                    return GridCategoryCard(
                      category: category,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => ResultPage(category: category),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      // Bottom navigation bar is now handled by MainLayout
    );
  }
}
