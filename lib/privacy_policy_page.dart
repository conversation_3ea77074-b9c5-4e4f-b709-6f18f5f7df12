import 'package:flutter/material.dart';
import 'services/chrome_custom_tabs_service.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Launch Privacy Policy in Chrome Custom Tabs
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ChromeCustomTabsService.launch(
        'https://technokdapps.blogspot.com/2025/05/terms-and-conditions-privacy-policy.html',
        title: 'Privacy Policy',
      );
      Navigator.of(context).pop();
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
      ),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
