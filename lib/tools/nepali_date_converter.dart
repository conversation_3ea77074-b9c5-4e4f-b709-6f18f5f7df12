import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:intl/intl.dart';

class NepaliDateConverter extends StatefulWidget {
  const NepaliDateConverter({super.key});

  @override
  State<NepaliDateConverter> createState() => _NepaliDateConverterState();
}

class _NepaliDateConverterState extends State<NepaliDateConverter> {
  final TextEditingController _adController = TextEditingController();
  final TextEditingController _bsController = TextEditingController();
  
  String _adResult = '';
  String _bsResult = '';

  @override
  void initState() {
    super.initState();
    // Set current dates
    final now = DateTime.now();
    final nepaliNow = NepaliDateTime.fromDateTime(now);
    
    _adController.text = DateFormat('yyyy-MM-dd').format(now);
    _bsController.text = '${nepaliNow.year}-${nepaliNow.month.toString().padLeft(2, '0')}-${nepaliNow.day.toString().padLeft(2, '0')}';
    
    _convertADToBS();
    _convertBSToAD();
  }

  @override
  void dispose() {
    _adController.dispose();
    _bsController.dispose();
    super.dispose();
  }

  void _convertADToBS() {
    try {
      final adDate = DateTime.parse(_adController.text);
      final nepaliDate = NepaliDateTime.fromDateTime(adDate);
      setState(() {
        _bsResult = '${nepaliDate.year}-${nepaliDate.month.toString().padLeft(2, '0')}-${nepaliDate.day.toString().padLeft(2, '0')}';
      });
    } catch (e) {
      setState(() {
        _bsResult = 'Invalid date format';
      });
    }
  }

  void _convertBSToAD() {
    try {
      final parts = _bsController.text.split('-');
      if (parts.length == 3) {
        final year = int.parse(parts[0]);
        final month = int.parse(parts[1]);
        final day = int.parse(parts[2]);
        
        final nepaliDate = NepaliDateTime(year, month, day);
        final adDate = nepaliDate.toDateTime();
        
        setState(() {
          _adResult = DateFormat('yyyy-MM-dd').format(adDate);
        });
      } else {
        setState(() {
          _adResult = 'Invalid date format';
        });
      }
    } catch (e) {
      setState(() {
        _adResult = 'Invalid date format';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Nepali Date Converter',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.indigo.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // AD to BS Section
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AD to BS Converter',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo.shade700,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _adController,
                      decoration: const InputDecoration(
                        labelText: 'Enter AD Date (YYYY-MM-DD)',
                        hintText: '2024-01-01',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      onChanged: (value) => _convertADToBS(),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.indigo.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.indigo.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'BS Date:',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.indigo.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _bsResult,
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.indigo.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // BS to AD Section
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'BS to AD Converter',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal.shade700,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _bsController,
                      decoration: const InputDecoration(
                        labelText: 'Enter BS Date (YYYY-MM-DD)',
                        hintText: '2081-01-01',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.calendar_month),
                      ),
                      onChanged: (value) => _convertBSToAD(),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.teal.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.teal.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'AD Date:',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.teal.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _adResult,
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.teal.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Info Card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Information',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• AD = Anno Domini (English Calendar)\n'
                      '• BS = Bikram Sambat (Nepali Calendar)\n'
                      '• Enter dates in YYYY-MM-DD format\n'
                      '• Conversion is automatic as you type',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
