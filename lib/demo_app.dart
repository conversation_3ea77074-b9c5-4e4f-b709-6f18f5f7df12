import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_fonts/google_fonts.dart';

void main() {
  runApp(const DemoApp());
}

class DemoApp extends StatelessWidget {
  const DemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Nepali Results',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      darkTheme: ThemeData.dark(useMaterial3: true).copyWith(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
      ),
      themeMode: ThemeMode.system,
      home: const HomeScreen(),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Define categories
    final categories = [
      CategoryData(
        name: 'SEE',
        description: 'Secondary Education Examination Results',
        color: const Color(0xFF1976D2), // Blue
        icon: Icons.school,
        url: 'http://see.gov.np/exam/results',
      ),
      CategoryData(
        name: 'HSEB',
        description: 'Higher Secondary Education Board Results',
        color: const Color(0xFF3F51B5), // Indigo
        icon: Icons.article_outlined,
        url: 'https://www.neb.gov.np/results',
      ),
      CategoryData(
        name: 'TU',
        description: 'Tribhuvan University Results',
        color: const Color(0xFF673AB7), // Deep Purple
        icon: Icons.account_balance,
        url: 'https://result.tuexam.edu.np/',
      ),
      CategoryData(
        name: 'KU',
        description: 'Kathmandu University Results',
        color: const Color(0xFF009688), // Teal
        icon: Icons.school_outlined,
        url: 'https://examresults.ku.edu.np:81/',
      ),
      CategoryData(
        name: 'PU',
        description: 'Purbanchal University Results',
        color: const Color(0xFF8BC34A), // Light Green
        icon: Icons.account_balance_outlined,
        url: 'https://puexam.edu.np/find-results_new',
      ),
      CategoryData(
        name: 'CTEVT',
        description:
            'Council for Technical Education and Vocational Training Results',
        color: const Color(0xFFFF9800), // Orange
        icon: Icons.school_outlined,
        url: 'https://itms.ctevt.org.np:5580/check_results',
      ),
      CategoryData(
        name: 'DV',
        description: 'Diversity Visa Lottery Results',
        color: const Color(0xFF4CAF50), // Green
        icon: Icons.public,
        url: 'https://dvprogram.state.gov/ESC/Default.aspx',
      ),
      CategoryData(
        name: 'IPO',
        description: 'Initial Public Offering Results',
        color: const Color(0xFFE91E63), // Pink
        icon: Icons.trending_up,
        url: 'https://iporesult.cdsc.com.np/',
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Nepali Results'),
        backgroundColor: Colors.lightBlue.shade200,
        elevation: 0,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Curved header with light blue background
            Container(
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.lightBlue.shade200, Colors.lightBlue.shade50],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(20),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Nepali Results',
                      style: GoogleFonts.poppins(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Check your results easily",
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.5,
                        color: Colors.blue.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Grid of result categories
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: GridView.builder(
                  physics: const BouncingScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount:
                        MediaQuery.of(context).size.width > 600 ? 3 : 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return CategoryCard(
                      category: category,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => ResultPage(category: category),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CategoryData {
  final String name;
  final String description;
  final Color color;
  final IconData icon;
  final String url;

  CategoryData({
    required this.name,
    required this.description,
    required this.color,
    required this.icon,
    required this.url,
  });
}

class CategoryCard extends StatelessWidget {
  final CategoryData category;
  final VoidCallback onTap;

  const CategoryCard({super.key, required this.category, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      elevation: isDarkMode ? 8 : 4,
      shadowColor: category.color.withAlpha(60),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        splashColor: category.color.withAlpha(50),
        highlightColor: category.color.withAlpha(30),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircleAvatar(
                backgroundColor: category.color.withAlpha(50),
                radius: 30,
                child: Icon(category.icon, size: 30, color: category.color),
              ),
              const SizedBox(height: 16),
              Text(
                category.name,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Text(
                  category.description,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ResultPage extends StatelessWidget {
  final CategoryData category;

  const ResultPage({super.key, required this.category});

  void _openUrl(BuildContext context, String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Could not launch URL')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('${category.name} Results')),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Official Website',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: InkWell(
                  onTap: () => _openUrl(context, category.url),
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: category.color.withAlpha(40),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            category.icon,
                            color: category.color,
                            size: 40,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Official Website',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: category.color,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Check results online',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: category.color.withAlpha(204),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: category.color,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
