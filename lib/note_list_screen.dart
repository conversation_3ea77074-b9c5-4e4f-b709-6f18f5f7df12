import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/note.dart';
import 'utils/notes_provider.dart';
import 'note_editor_screen.dart';

class NoteListScreen extends StatelessWidget {
  const NoteListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'My Notes',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              final notesProvider = Provider.of<NotesProvider>(
                context,
                listen: false,
              );
              notesProvider.createNote();

              // Get the newly created note
              final newNote = notesProvider.currentNote;
              if (newNote != null) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => NoteEditorScreen(note: newNote),
                  ),
                );
              }
            },
          ),
        ],
      ),
      // Notebook-style background
      body: Container(
        decoration: BoxDecoration(
          // Lined paper effect
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E) // Dark mode background
                  : const Color(0xFFF8F8F8), // Light mode background
          // Adding horizontal lines with a decoration
          boxShadow: [
            for (double i = 0; i < 30; i++)
              BoxShadow(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withAlpha(8) // 0.03 * 255 ≈ 8
                        : Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                offset: Offset(0, 24.0 * i),
                spreadRadius: 0.5,
                blurRadius: 0,
              ),
          ],
        ),
        child: Consumer<NotesProvider>(
          builder: (context, notesProvider, child) {
            if (notesProvider.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (notesProvider.notes.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.note_alt_outlined,
                      size: 64,
                      color: Theme.of(
                        context,
                      ).colorScheme.primary.withAlpha(100),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No notes yet',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Tap the + button to create a new note',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white70
                                : Colors.black54,
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: notesProvider.notes.length,
              itemBuilder: (context, index) {
                final note = notesProvider.notes[index];
                return _buildNoteCard(context, note);
              },
            );
          },
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          final notesProvider = Provider.of<NotesProvider>(
            context,
            listen: false,
          );
          notesProvider.createNote();

          // Get the newly created note
          final newNote = notesProvider.currentNote;
          if (newNote != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NoteEditorScreen(note: newNote),
              ),
            );
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildNoteCard(BuildContext context, Note note) {
    // Get the first few lines of content for preview
    final contentPreview = note.content.split('\n').take(2).join('\n');

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // Select this note and open the editor
          final notesProvider = Provider.of<NotesProvider>(
            context,
            listen: false,
          );
          notesProvider.selectNote(note.id);

          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NoteEditorScreen(note: note),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title and date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      note.title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    '${note.updatedAt.day}/${note.updatedAt.month}/${note.updatedAt.year}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Content preview
              Text(
                contentPreview,
                style: GoogleFonts.dancingScript(
                  fontSize: 16,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey[400]
                          : Colors.grey[700],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // Actions
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.delete_outline, size: 20),
                    onPressed: () {
                      // Show confirmation dialog
                      showDialog(
                        context: context,
                        builder:
                            (context) => AlertDialog(
                              title: Text(
                                'Delete Note',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              content: Text(
                                'Are you sure you want to delete this note?',
                                style: GoogleFonts.poppins(),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: Text(
                                    'Cancel',
                                    style: GoogleFonts.poppins(),
                                  ),
                                ),
                                TextButton(
                                  onPressed: () {
                                    // Delete the note
                                    final notesProvider =
                                        Provider.of<NotesProvider>(
                                          context,
                                          listen: false,
                                        );
                                    notesProvider.deleteNote(note.id);
                                    Navigator.pop(context);
                                  },
                                  child: Text(
                                    'Delete',
                                    style: GoogleFonts.poppins(
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit_outlined, size: 20),
                    onPressed: () {
                      // Select this note and open the editor
                      final notesProvider = Provider.of<NotesProvider>(
                        context,
                        listen: false,
                      );
                      notesProvider.selectNote(note.id);

                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => NoteEditorScreen(note: note),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
