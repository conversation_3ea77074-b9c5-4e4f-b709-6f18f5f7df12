import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DateDisplay extends StatefulWidget {
  final bool isCompact;

  const DateDisplay({super.key, this.isCompact = false});

  @override
  State<DateDisplay> createState() => _DateDisplayState();
}

class _DateDisplayState extends State<DateDisplay> {
  late NepaliDateTime _currentNepaliDate;
  String? _cachedDateText;

  @override
  void initState() {
    super.initState();
    _loadCachedDate();
    _updateDate();
    // Update date every minute to keep it current
    _startDateTimer();
  }

  Future<void> _loadCachedDate() async {
    final prefs = await SharedPreferences.getInstance();
    final cachedDate = prefs.getString('nepali_date_display');
    final lastUpdateDate = prefs.getString('nepali_date_last_update');

    // Check if cached date is from today
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    if (cachedDate != null && lastUpdateDate == todayString) {
      setState(() {
        _cachedDateText = cachedDate;
      });
    }
  }

  Future<void> _saveDateToCache(String dateText) async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now();
    final todayString = '${today.year}-${today.month}-${today.day}';

    await prefs.setString('nepali_date_display', dateText);
    await prefs.setString('nepali_date_last_update', todayString);
  }

  void _updateDate() {
    setState(() {
      _currentNepaliDate = NepaliDateTime.now();
    });

    // Save to cache when date updates
    final dateText = _formatNepaliDate();
    _saveDateToCache(dateText);
  }

  void _startDateTimer() {
    // Update every minute
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _updateDate();
        _startDateTimer();
      }
    });
  }

  String _formatNepaliDate() {
    // Format the Nepali date properly
    final nepaliYear = _currentNepaliDate.year;
    final nepaliMonth = NepaliDateFormat.MMMM().format(_currentNepaliDate);
    final nepaliDay = _currentNepaliDate.day;
    final nepaliWeekday = NepaliDateFormat.EEEE().format(_currentNepaliDate);

    // Convert numbers to Nepali numerals
    final nepaliNumerals = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
    String convertToNepaliNumerals(int number) {
      return number.toString().split('').map((digit) {
        return nepaliNumerals[int.parse(digit)];
      }).join('');
    }

    final nepaliYearStr = convertToNepaliNumerals(nepaliYear);
    final nepaliDayStr = convertToNepaliNumerals(nepaliDay);

    // Format: "बिहीबार, २०८२ जेठ २९" (weekday, year month day - all in Nepali)
    return '$nepaliWeekday, $nepaliYearStr $nepaliMonth $nepaliDayStr';
  }

  @override
  Widget build(BuildContext context) {
    final dateText = _cachedDateText ?? _formatNepaliDate();

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.calendar_today,
          size: widget.isCompact ? 16 : 18,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white
              : Colors.white,
        ),
        const SizedBox(width: 8),
        Flexible(
          child: Text(
            dateText,
            style: GoogleFonts.poppins(
              fontSize: widget.isCompact ? 14 : 16,
              fontWeight: widget.isCompact ? FontWeight.w500 : FontWeight.w600,
              color: Theme.of(context).brightness == Brightness.dark
                  ? Colors.white
                  : Colors.white,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
