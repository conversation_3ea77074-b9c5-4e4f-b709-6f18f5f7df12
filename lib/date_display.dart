import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class DateDisplay extends StatelessWidget {
  final bool isCompact;

  const DateDisplay({super.key, this.isCompact = true});

  @override
  Widget build(BuildContext context) {
    // Get current date
    final now = DateTime.now();

    // Nepali day names
    final nepaliDayNames = [
      'आइतबार', // Sunday
      'सोमबार', // Monday
      'मंगलबार', // Tuesday
      'बुधबार', // Wednesday
      'बिहीबार', // Thursday
      'शुक्रबार', // Friday
      'शनिबार', // Saturday
    ];

    // Nepali month names
    final nepaliMonthNames = [
      'बैशाख', 'जेठ', 'असार', 'साउन', 'भदौ', 'असोज',
      'कार्तिक', 'मंसिर', 'पुष', 'माघ', 'फागुन', 'चैत'
    ];

    // Simple conversion to approximate Nepali date (this is a simplified version)
    // For a production app, you would use a proper Nepali calendar library
    final nepaliYear = now.year + 57; // Approximate conversion
    final nepaliMonth = (now.month + 8) % 12; // Approximate month conversion
    final nepaliDay = now.day; // Simplified day conversion
    final dayOfWeek = now.weekday % 7;

    final nepaliDayName = nepaliDayNames[dayOfWeek];
    final nepaliMonthName = nepaliMonthNames[nepaliMonth];

    // Convert numbers to Nepali numerals
    final nepaliNumerals = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
    String convertToNepaliNumerals(int number) {
      return number.toString().split('').map((digit) {
        return nepaliNumerals[int.parse(digit)];
      }).join('');
    }

    final nepaliYearStr = convertToNepaliNumerals(nepaliYear);
    final nepaliDayStr = convertToNepaliNumerals(nepaliDay);

    // Format Nepali date
    final dateText = '$nepaliYearStr साल $nepaliMonthName $nepaliDayStr, $nepaliDayName';

    return Text(
      dateText,
      style: GoogleFonts.poppins(
        fontSize: isCompact ? 14 : 16,
        fontWeight: isCompact ? FontWeight.w500 : FontWeight.w600,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }
}
