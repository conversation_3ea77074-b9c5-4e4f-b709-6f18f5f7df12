import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nepali_utils/nepali_utils.dart';

class DateDisplay extends StatefulWidget {
  final bool isCompact;

  const DateDisplay({super.key, this.isCompact = false});

  @override
  State<DateDisplay> createState() => _DateDisplayState();
}

class _DateDisplayState extends State<DateDisplay> {
  late NepaliDateTime _currentNepaliDate;

  @override
  void initState() {
    super.initState();
    _updateDate();
    // Update date every minute to keep it current
    _startDateTimer();
  }

  void _updateDate() {
    setState(() {
      _currentNepaliDate = NepaliDateTime.now();
    });
  }

  void _startDateTimer() {
    // Update every minute
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _updateDate();
        _startDateTimer();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Format the Nepali date properly
    final nepaliYear = _currentNepaliDate.year;
    final nepaliMonth = NepaliDateFormat.MMMM().format(_currentNepaliDate);
    final nepaliDay = _currentNepaliDate.day;
    final nepaliWeekday = NepaliDateFormat.EEEE().format(_currentNepaliDate);

    // Convert numbers to Nepali numerals
    final nepaliNumerals = ['०', '१', '२', '३', '४', '५', '६', '७', '८', '९'];
    String convertToNepaliNumerals(int number) {
      return number.toString().split('').map((digit) {
        return nepaliNumerals[int.parse(digit)];
      }).join('');
    }

    final nepaliYearStr = convertToNepaliNumerals(nepaliYear);
    final nepaliDayStr = convertToNepaliNumerals(nepaliDay);

    // Format: "2082 साल जेठ 29, बिहीबार"
    final dateText = '$nepaliYearStr साल $nepaliMonth $nepaliDayStr, $nepaliWeekday';

    return Text(
      dateText,
      style: GoogleFonts.poppins(
        fontSize: widget.isCompact ? 14 : 16,
        fontWeight: widget.isCompact ? FontWeight.w500 : FontWeight.w600,
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white
            : Colors.white,
      ),
      overflow: TextOverflow.ellipsis,
    );
  }
}
