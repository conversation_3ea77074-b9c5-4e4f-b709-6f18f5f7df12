import 'package:flutter/material.dart';
import 'dart:async';
import 'package:google_fonts/google_fonts.dart';
import 'main_layout.dart';
import '../utils/constants.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();

    Timer(const Duration(seconds: 2), () {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainLayout()),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    // Get the current theme
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;

    // Background color based on theme
    final backgroundColor =
        isDarkMode
            ? const Color(0xFF0A0A1A) // Dark theme background
            : Colors.white; // Light theme background

    // Text colors based on theme
    final titleColor = isDarkMode ? Colors.white : primaryColor;
    final subtitleColor =
        isDarkMode
            ? const Color(0xFF03DAC6) // Teal accent for dark mode
            : AppConstants.secondaryColor; // Secondary color for light mode

    return Scaffold(
      backgroundColor: backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Add space at the top for better vertical centering
            const SizedBox(height: 40),
            // Student icon with increased size (60% larger)
            Icon(
              Icons.school,
              size: 128, // Increased from 80 to 128 (60% increase)
              color: primaryColor,
            ),
            const SizedBox(height: 24),
            // "Nepali Results" text with theme-appropriate color
            Text(
              AppConstants.appName,
              style: GoogleFonts.poppins(
                fontSize: 36, // Larger font size
                fontWeight: FontWeight.bold,
                color: titleColor,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 8),
            // Subtitle text with theme-appropriate color
            Text(
              'SEE, HSEB, TU, KU, PU, CTEVT, DV',
              style: GoogleFonts.poppins(
                fontSize: 16, // Slightly larger font size
                fontWeight: FontWeight.w600,
                color: subtitleColor,
                letterSpacing: 0.5,
              ),
            ),
            const SizedBox(height: 32),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
            ),
          ],
        ),
      ),
    );
  }
}
