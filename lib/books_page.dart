import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'services/chrome_custom_tabs_service.dart';

class BooksPage extends StatefulWidget {
  const BooksPage({super.key});

  @override
  State<BooksPage> createState() => _BooksPageState();
}

class _BooksPageState extends State<BooksPage> {
  final List<Map<String, dynamic>> _classes = [
    {
      'name': 'Class 10',
      'color': Colors.purple,
      'mediums': [
        {'name': 'English Medium', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
        {'name': 'Nepali Medium', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
      ],
    },
    {
      'name': 'Class 11',
      'color': Colors.orange,
      'mediums': [
        {'name': 'English Medium', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
        {'name': 'Nepali Medium', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
      ],
    },
    {
      'name': 'Class 12',
      'color': Colors.red,
      'mediums': [
        {'name': 'English Medium', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
        {'name': 'Nepali Medium', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
      ],
    },
  ];

  Future<void> _launchChromeCustomTab(String url, String title) async {
    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              ),
              const SizedBox(width: 16),
              Text('Opening $title...'),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }

    try {
      await ChromeCustomTabsService.launchTextbookPDF(url, title);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open $title. Please check your internet connection.'),
            backgroundColor: Colors.red.shade600,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _launchChromeCustomTab(url, title),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Textbooks',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Loading message
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber.shade50,
              border: Border.all(color: Colors.amber.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Some books may take longer to load due to internet speed or source. We\'re working to improve this.',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.amber.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Class list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _classes.length,
              itemBuilder: (context, index) {
                final classData = _classes[index];
                return _buildClassCard(classData);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassCard(Map<String, dynamic> classData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          expansionTileTheme: ExpansionTileThemeData(
            backgroundColor: Colors.transparent,
            collapsedBackgroundColor: Colors.transparent,
            iconColor: Colors.white,
            collapsedIconColor: Colors.white,
            textColor: Colors.white,
            collapsedTextColor: Colors.white,
          ),
        ),
        child: ExpansionTile(
          tilePadding: EdgeInsets.zero,
          childrenPadding: const EdgeInsets.only(bottom: 16, left: 16, right: 16),
          title: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: classData['color'],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.school, color: Colors.white, size: 30),
                const SizedBox(width: 12),
                Text(
                  classData['name'],
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Text(
                  'Tap to expand',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          children: [
            const SizedBox(height: 16),
            for (var medium in classData['mediums'])
              _buildMediumButton(medium, classData['color']),
          ],
        ),
      ),
    );
  }

  Widget _buildMediumButton(Map<String, dynamic> medium, Color color) {
    final bool isNepaliMedium = medium['name'].contains('Nepali');

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: SizedBox(
        width: double.infinity,
        height: 60,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: isNepaliMedium ? Colors.red.shade700 : Colors.blue.shade700,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 4,
          ),
          onPressed: () {
            _launchChromeCustomTab(medium['url'], '${medium['name']} Books');
          },
          child: Row(
            children: [
              // Flag icon
              Container(
                width: 30,
                height: 20,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: isNepaliMedium ? Colors.red : Colors.blue,
                ),
                child: Center(
                  child: Text(
                    isNepaliMedium ? 'नेपाली' : 'EN',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Medium name
              Text(
                medium['name'],
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              // Arrow icon
              const Icon(Icons.arrow_forward_ios, size: 18),
            ],
          ),
        ),
      ),
    );
  }
}

