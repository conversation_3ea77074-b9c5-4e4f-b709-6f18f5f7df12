import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:webview_flutter/webview_flutter.dart';

class BooksPage extends StatefulWidget {
  const BooksPage({super.key});

  @override
  State<BooksPage> createState() => _BooksPageState();
}

class _BooksPageState extends State<BooksPage> {
  final List<Map<String, dynamic>> _classes = [
    {
      'name': 'Class 10',
      'color': Colors.purple,
      'mediums': [
        {'name': 'English Medium', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Nepali Medium', 'url': 'https://moecdc.gov.np/en/text-books'},
      ],
    },
    {
      'name': 'Class 11',
      'color': Colors.orange,
      'mediums': [
        {'name': 'English Medium', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Nepali Medium', 'url': 'https://moecdc.gov.np/en/text-books'},
      ],
    },
    {
      'name': 'Class 12',
      'color': Colors.red,
      'mediums': [
        {'name': 'English Medium', 'url': 'https://moecdc.gov.np/en/text-books'},
        {'name': 'Nepali Medium', 'url': 'https://moecdc.gov.np/en/text-books'},
      ],
    },
  ];

  Future<void> _launchWebView(String url, String title) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewScreen(url: url, title: title),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Textbooks',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Loading message
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.amber.shade50,
              border: Border.all(color: Colors.amber.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.amber.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Some books may take longer to load due to internet speed or source. We\'re working to improve this.',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.amber.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Class list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _classes.length,
              itemBuilder: (context, index) {
                final classData = _classes[index];
                return _buildClassCard(classData);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClassCard(Map<String, dynamic> classData) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: classData['color'],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.school, color: Colors.white, size: 30),
                const SizedBox(width: 12),
                Text(
                  classData['name'],
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          // Medium buttons
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                for (var medium in classData['mediums'])
                  _buildMediumButton(medium, classData['color']),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediumButton(Map<String, dynamic> medium, Color color) {
    final bool isNepaliMedium = medium['name'].contains('Nepali');

    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: SizedBox(
        width: double.infinity,
        height: 60,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: isNepaliMedium ? Colors.red.shade700 : Colors.blue.shade700,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 4,
          ),
          onPressed: () {
            _launchWebView(medium['url'], '${medium['name']} Books');
          },
          child: Row(
            children: [
              // Flag icon
              Container(
                width: 30,
                height: 20,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: isNepaliMedium ? Colors.red : Colors.blue,
                ),
                child: Center(
                  child: Text(
                    isNepaliMedium ? 'नेपाली' : 'EN',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Medium name
              Text(
                medium['name'],
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              // Arrow icon
              const Icon(Icons.arrow_forward_ios, size: 18),
            ],
          ),
        ),
      ),
    );
  }
}

class WebViewScreen extends StatefulWidget {
  final String url;
  final String title;

  const WebViewScreen({super.key, required this.url, required this.title});

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late final WebViewController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}