import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/textbook.dart';
import '../utils/textbooks_data.dart';
import '../utils/flag_painters.dart';
import 'medium_books_screen.dart';

class BooksPage extends StatefulWidget {
  const BooksPage({super.key});

  @override
  State<BooksPage> createState() => _BooksPageState();
}

class _BooksPageState extends State<BooksPage> {
  int? _expandedIndex;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Textbooks',
          style: GoogleFonts.poppins(
            fontSize: 22, // Larger font
            fontWeight: FontWeight.bold,
          ),
        ),
        toolbarHeight: 70, // Taller app bar
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          // Wooden texture effect
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E) // Dark mode background
                  : const Color(0xFFF5F5DC), // Light beige for light mode
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF2A2A2A)
                  : const Color(0xFFE6E6C8),
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E)
                  : const Color(0xFFF5F5DC),
            ],
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: TextbooksData.classes.length,
          itemBuilder: (context, index) {
            final textbookClass = TextbooksData.classes[index];
            return _buildClassCard(context, textbookClass, index);
          },
        ),
      ),
    );
  }

  Widget _buildClassCard(
    BuildContext context,
    TextbookClass textbookClass,
    int index,
  ) {
    final bool isExpanded = _expandedIndex == index;
    final Color cardColor = _getClassColor(textbookClass.className, context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0), // Reduced from 24.0
      elevation: 4, // Reduced from 6
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ), // Reduced from 16
      child: Column(
        children: [
          // Header
          InkWell(
            borderRadius: BorderRadius.circular(12), // Reduced from 16
            onTap: () {
              setState(() {
                _expandedIndex = isExpanded ? null : index;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 18.0, // Reduced from 24.0
                horizontal: 16.0, // Reduced from 20.0
              ),
              decoration: BoxDecoration(
                color: cardColor.withAlpha(230),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(12), // Reduced from 16
                  topRight: const Radius.circular(12), // Reduced from 16
                  bottomLeft: Radius.circular(
                    isExpanded ? 0 : 12,
                  ), // Reduced from 16
                  bottomRight: Radius.circular(
                    isExpanded ? 0 : 12,
                  ), // Reduced from 16
                ),
                boxShadow: [
                  BoxShadow(
                    color: cardColor.withAlpha(50), // Reduced from 60
                    blurRadius: 6, // Reduced from 8
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.school,
                    color: Colors.white,
                    size: 30,
                  ), // Reduced from 36
                  const SizedBox(width: 12), // Reduced from 16
                  Expanded(
                    child: Text(
                      textbookClass.className,
                      style: GoogleFonts.poppins(
                        fontSize: 20, // Reduced from 24
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.white,
                    size: 28, // Reduced from 32
                  ),
                ],
              ),
            ),
          ),

          // Expanded content
          if (isExpanded)
            Container(
              padding: const EdgeInsets.all(18.0), // Reduced from 24.0
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Medium',
                    style: GoogleFonts.poppins(
                      fontSize: 18, // Reduced from 20
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16), // Reduced from 24
                  // Medium options
                  ...textbookClass.mediums.map(
                    (medium) => _buildMediumButton(
                      context,
                      textbookClass,
                      medium,
                      cardColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMediumButton(
    BuildContext context,
    TextbookClass textbookClass,
    TextbookMedium medium,
    Color cardColor,
  ) {
    final bool isNepaliMedium = medium.mediumName.contains('Nepali');

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0), // Reduced from 20.0
      child: SizedBox(
        width: double.infinity,
        height: 70, // Reduced from 80
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor:
                isNepaliMedium
                    ? const Color(0xFFC8102E) // Red for Nepal flag
                    : const Color(0xFF012169), // Navy blue for UK flag
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 20,
            ), // Reduced padding
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12), // Reduced from 14
            ),
            elevation: 4, // Reduced from 5
            shadowColor:
                isNepaliMedium
                    ? const Color(0xFFC8102E).withAlpha(80)
                    : const Color(0xFF012169).withAlpha(80),
          ),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => MediumBooksScreen(
                      className: textbookClass.className,
                      medium: medium,
                    ),
              ),
            );
          },
          child: Row(
            children: [
              // Flag icon
              Container(
                width: 40,
                height: 30,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(50),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child:
                      isNepaliMedium
                          ? _buildNepalFlag() // Nepal flag
                          : _buildUKFlag(), // UK flag
                ),
              ),
              const SizedBox(width: 16),
              // Medium name
              Text(
                medium.mediumName,
                style: GoogleFonts.poppins(
                  fontSize: 20, // Reduced from 22
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                ),
              ),
              const Spacer(),
              // Arrow icon
              const Icon(Icons.arrow_forward_ios, size: 18),
            ],
          ),
        ),
      ),
    );
  }

  // Nepal flag widget
  Widget _buildNepalFlag() {
    return Image.asset(
      'assets/images/Flag_of_Nepal.png',
      width: 40,
      height: 30,
      fit: BoxFit.contain,
    );
  }

  // UK flag widget
  Widget _buildUKFlag() {
    return CustomPaint(size: const Size(40, 30), painter: UKFlagPainter());
  }

  Color _getClassColor(String className, BuildContext context) {
    switch (className) {
      case 'Class 8':
        return const Color(0xFF1565C0); // Blue
      case 'Class 9':
        return const Color(0xFF00897B); // Teal
      case 'Class 10':
        return const Color(0xFF7B1FA2); // Purple
      case 'Class 11':
        return const Color(0xFFEF6C00); // Orange
      case 'Class 12':
        return const Color(0xFFC62828); // Red
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }
}
