import 'package:flutter/material.dart';
import 'services/chrome_custom_tabs_service.dart';

class TermsPage extends StatelessWidget {
  const TermsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Launch Terms and Conditions in Chrome Custom Tabs
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ChromeCustomTabsService.launch(
        'https://technokdapps.blogspot.com/2025/05/terms-and-conditions-body-font-family.html',
        title: 'Terms and Conditions',
      );
      Navigator.of(context).pop();
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms and Conditions'),
      ),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
