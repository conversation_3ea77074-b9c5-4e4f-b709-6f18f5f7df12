import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'notification_item.dart';
import 'notification_manager.dart';
import 'ipo_notification_service.dart';

/// Provider class to manage notification settings and state
class NotificationProvider extends ChangeNotifier {
  // Centralized notification manager
  final NotificationManager _notificationManager = NotificationManager();

  // IPO notification service
  final IpoNotificationService _ipoNotificationService =
      IpoNotificationService();

  // Notification settings
  bool _areNotificationsEnabled = true;

  // Notification count (for badge)
  int _notificationCount = 0;

  // Getters
  bool get areNotificationsEnabled => _areNotificationsEnabled;

  // Get notification count for badge
  int get notificationCount => _notificationCount;

  // Get notifications
  List<NotificationItem> get notifications =>
      _notificationManager.notifications;

  // Stream getters
  Stream<String?> get notificationClickStream =>
      _notificationManager.notificationClickStream;
  Stream<NotificationItem?> get notificationDataStream =>
      _notificationManager.notificationDataStream;

  /// Initialize the notification provider
  Future<void> initialize() async {
    try {
      // Initialize notification manager
      await _notificationManager.initialize();

      // Load notification settings
      await _loadNotificationSettings();

      // Listen for notification clicks
      _notificationManager.notificationClickStream.listen(
        handleNotificationClick,
      );

      // Update notification count
      _updateNotificationCount();

      // Subscribe to topics based on settings
      await _updateTopicSubscriptions();

      // Initialize IPO notification service
      await _ipoNotificationService.initialize(this);
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing notification provider: $e');
      }
    }
  }

  /// Load notification settings from shared preferences
  Future<void> _loadNotificationSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _areNotificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
    notifyListeners();
  }

  /// Save notification settings to shared preferences
  Future<void> _saveNotificationSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', _areNotificationsEnabled);
  }

  /// Update FCM topic subscriptions based on settings
  Future<void> _updateTopicSubscriptions() async {
    // Subscribe to all topics if notifications are enabled
    try {
      if (_areNotificationsEnabled) {
        await _notificationManager.subscribeToTopic('all');
      } else {
        await _notificationManager.unsubscribeFromTopic('all');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating topic subscriptions: $e');
      }
      // Continue with the app even if topic subscription fails
    }
  }

  /// Toggle main notifications setting
  Future<void> toggleNotifications(bool value) async {
    _areNotificationsEnabled = value;
    await _saveNotificationSettings();
    await _updateTopicSubscriptions();
    notifyListeners();
  }

  /// Update the notification count
  void _updateNotificationCount() {
    _notificationCount =
        _notificationManager.notifications.where((n) => !n.isRead).length;
    notifyListeners();
  }

  /// Show a local notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    if (!_areNotificationsEnabled) return;

    await _notificationManager.showNotification(
      id: id,
      title: title,
      body: body,
      data: data,
    );

    // Update notification count
    _updateNotificationCount();
  }

  /// Mark a notification as read
  void markNotificationAsRead(String id) {
    _notificationManager.markNotificationAsRead(id);
    _updateNotificationCount();
  }

  /// Mark all notifications as read
  void markAllNotificationsAsRead() {
    _notificationManager.markAllNotificationsAsRead();
    _updateNotificationCount();
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _notificationManager.clearAllNotifications();
    _updateNotificationCount();
  }

  /// Clear notification count (e.g., when user views notifications)
  void clearNotificationCount() {
    if (_notificationCount > 0) {
      _notificationCount = 0;
      notifyListeners();
    }
  }

  /// Simulate receiving a new notification (for testing)
  Future<void> simulateNewNotification() async {
    await _notificationManager.simulateNewNotification();
    _updateNotificationCount();
  }

  /// Check for new IPO results
  Future<void> checkForNewIpoResults() async {
    await _ipoNotificationService.checkNow();
  }

  /// Add a notification
  void addNotification(NotificationItem notification) {
    _notificationManager.addNotification(notification);
    _updateNotificationCount();
    notifyListeners();
  }

  /// Schedule a notification for a future time
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    Map<String, dynamic>? data,
  }) async {
    if (!_areNotificationsEnabled) return;

    await _notificationManager.scheduleNotification(
      id: id,
      title: title,
      body: body,
      scheduledDate: scheduledDate,
      data: data,
    );
  }

  /// Handle notification click
  void handleNotificationClick(String? payload) {
    if (payload == null) return;

    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;

      // Get notification ID
      final String? notificationId = data['notification_id'] as String?;

      // Mark notification as read if it exists
      if (notificationId != null) {
        markNotificationAsRead(notificationId);
      }

      // Handle different notification types
      final String? type = data['type'] as String?;

      if (kDebugMode) {
        print('Handling notification click: $type');
        print('Notification data: $data');
      }

      // You can add specific handling logic here based on notification type
    } catch (e) {
      if (kDebugMode) {
        print('Error handling notification click: $e');
      }
    }
  }

  @override
  void dispose() {
    _notificationManager.dispose();
    super.dispose();
  }
}
